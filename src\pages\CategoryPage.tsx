import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useState, useEffect } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import PaginatedGrid from "@/components/PaginatedGrid";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ArrowUp } from "lucide-react";
import { scrollToTop } from "@/utils/scrollToTop";
import { MediaItem } from "@/types/media";
import apiService from "@/services/apiService";

// Categories mapping for URL to display name
const CATEGORY_MAPPING: { [key: string]: string } = {
  "hindi-movies": "Hindi Movies",
  "hindi-web-series": "Hindi Web Series",
  "english-movies": "English Movies", 
  "english-web-series": "English Web Series",
  "telugu-movies": "Telugu Movies",
  "telugu-web-series": "Telugu Web Series",
  "tamil-movies": "Tamil Movies",
  "tamil-web-series": "Tamil Web Series",
  "malayalam-movies": "Malayalam Movies",
  "malayalam-web-series": "Malayalam Web Series",
  "korean-movies": "Korean Movies",
  "korean-web-series": "Korean Web Series",
  "japanese-movies": "Japanese Movies",
  "japanese-web-series": "Japanese Web Series",
  "anime": "Anime",
  "hindi-dubbed": "Hindi Dubbed",
  "english-dubbed": "English Dubbed",
  "animation": "Animation",
  "new-releases": "New Releases",
  "requested": "Requested",
  "drama": "Drama"
};

export default function CategoryPage() {
  const { categorySlug } = useParams<{ categorySlug: string }>();
  const [content, setContent] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showBackToTop, setShowBackToTop] = useState(false);

  useEffect(() => {
    const fetchCategoryContent = async () => {
      if (!categorySlug || !CATEGORY_MAPPING[categorySlug]) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const categoryName = CATEGORY_MAPPING[categorySlug];
        let allContent = [];

        // Special handling for "New Releases", "Requested", and "Drama" categories
        if (categoryName === "New Releases" || categoryName === "Requested" || categoryName === "Drama") {
          try {
            // Get content from sections first
            const sectionsResponse = await apiService.getSections({ active_only: true });
            if (sectionsResponse && sectionsResponse.success && Array.isArray(sectionsResponse.data)) {
              // Find the corresponding section
              const targetSection = sectionsResponse.data.find(section => 
                section.name === categoryName || 
                section.slug === categorySlug ||
                section.name.toLowerCase().includes(categoryName.toLowerCase())
              );

              if (targetSection) {
                const sectionContentResponse = await apiService.getSectionContent(targetSection.id, {
                  published_only: true,
                  limit: 1000,
                  sort_by: 'created_at',
                  sort_order: 'desc'
                });

                if (sectionContentResponse && sectionContentResponse.success && Array.isArray(sectionContentResponse.data)) {
                  allContent = [...sectionContentResponse.data];
                }
              }
            }
          } catch (sectionError) {
            console.warn('Failed to fetch section content for special category:', sectionError);
          }

          // Also get content directly assigned to this category
          try {
            const categoryResponse = await apiService.getContent({
              category: categoryName,
              published: true,
              limit: 1000,
              sort_by: 'created_at',
              sort_order: 'desc'
            });

            if (categoryResponse && categoryResponse.success && Array.isArray(categoryResponse.data)) {
              // Merge with section content, avoiding duplicates
              const existingIds = new Set(allContent.map(item => item.id));
              const newContent = categoryResponse.data.filter(item => !existingIds.has(item.id));
              allContent = [...allContent, ...newContent];
            }
          } catch (categoryError) {
            console.warn('Failed to fetch category content for special category:', categoryError);
          }
        } else {
          // Regular category handling
          try {
            const response = await apiService.getContentByCategory(categorySlug, 1, 1000);
            if (response && response.success && Array.isArray(response.data)) {
              allContent = response.data;
            }
          } catch (error) {
            console.warn('Category-specific endpoint failed, falling back to general content endpoint:', error);
            // Fallback to general content endpoint
            const response = await apiService.getContent({
              category: categoryName,
              published: true,
              limit: 1000,
              sort_by: 'created_at',
              sort_order: 'desc'
            });

            if (response && response.success && Array.isArray(response.data)) {
              allContent = response.data;
            }
          }
        }

        // Sort the final content by creation date (newest first)
        allContent.sort((a, b) => new Date(b.created_at || b.createdAt || 0) - new Date(a.created_at || a.createdAt || 0));

        setContent(allContent);
      } catch (error) {
        console.error('Error fetching category content:', error);
        setError('Failed to load content');
        setContent([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCategoryContent();
  }, [categorySlug]);

  // Back to Top functionality
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  if (!categorySlug || !CATEGORY_MAPPING[categorySlug]) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <main className="flex-1 max-w-7xl w-full mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-foreground mb-4">Category Not Found</h1>
            <p className="text-muted-foreground mb-6">The requested category does not exist.</p>
            <Link to="/categories" onClick={scrollToTop}>
              <Button>Browse Categories</Button>
            </Link>
          </div>
        </main>
        <Footer />
        
        {/* Back to Top Button */}
        {showBackToTop && (
          <Button
            onClick={scrollToTop}
            className="fixed bottom-4 right-4 sm:bottom-6 sm:right-6 z-40 h-12 w-12 rounded-full bg-primary hover:bg-primary/90 shadow-lg transition-all duration-300"
            size="icon"
            aria-label="Back to top"
          >
            <ArrowUp className="h-5 w-5" />
          </Button>
        )}
      </div>
    );
  }

  const categoryName = CATEGORY_MAPPING[categorySlug];

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />
      
      <main className="flex-1 max-w-7xl w-full mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1
            className="stdb-heading"
            style={{
              color: "#e6cb8e",
              fontFamily: "'Koulen', Impact, Arial, sans-serif",
              fontWeight: 400,
              // Reduced font size by 25% for mobile and made it responsive to fit in one line
              fontSize: "clamp(1.125rem, 4vw, 2.25rem)", // Mobile: 1.125rem (25% smaller), Desktop: 2.25rem
              letterSpacing: "0.07em",
              margin: 0,
              textTransform: "uppercase",
              textShadow: "0 2px 16px #19191740, 0 1px 2px #0002",
              // Ensure single line display
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              maxWidth: "100%"
            }}
          >
            {categoryName}
          </h1>
          <div className="flex gap-2">
            <Link to="/categories" onClick={scrollToTop}>
              <Button variant="outline" className="flex items-center gap-2">
                <ArrowLeft className="w-4 h-4" />
                All Categories
              </Button>
            </Link>
            <Link to="/" onClick={scrollToTop}>
              <Button variant="outline" className="flex items-center gap-2">
                <ArrowLeft className="w-4 h-4" />
                Home
              </Button>
            </Link>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading {categoryName} content...</p>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold text-foreground mb-4">Error Loading Content</h2>
            <p className="text-muted-foreground mb-6">{error}</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        )}

        {/* Content Grid with Pagination */}
        {!loading && !error && content.length > 0 && (
          <PaginatedGrid items={content} pageSize={50} />
        )}

        {/* No Content State */}
        {!loading && !error && content.length === 0 && (
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold text-foreground mb-4">No Content Available</h2>
            <p className="text-muted-foreground mb-6">
              There is currently no content available in the {categoryName} category.
            </p>
            <Link to="/categories" onClick={scrollToTop}>
              <Button>Browse Other Categories</Button>
            </Link>
          </div>
        )}

        {/* Content count with pagination info */}
        {!loading && !error && content.length > 0 && (
          <div className="mt-8 text-center">
            <p className="text-muted-foreground text-sm">
              Total {content.length} {content.length === 1 ? 'item' : 'items'} in {categoryName}
              {content.length > 50 && ' - Showing 50 items per page'}
            </p>
          </div>
        )}
      </main>

      <Footer />
      
      {/* Back to Top Button */}
      {showBackToTop && (
        <Button
          onClick={scrollToTop}
          className="fixed bottom-4 right-4 sm:bottom-6 sm:right-6 z-40 h-12 w-12 rounded-full bg-primary hover:bg-primary/90 shadow-lg transition-all duration-300"
          size="icon"
          aria-label="Back to top"
        >
          <ArrowUp className="h-5 w-5" />
        </Button>
      )}
    </div>
  );
}