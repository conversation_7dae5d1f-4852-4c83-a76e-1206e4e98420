
import React, { useState, useEffect } from "react";
import HeroCarousel from "@/components/HeroCarousel";
import CardGrid from "@/components/CardGrid";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
// Adblocker components removed
import ImageDiagnostics from "@/components/ImageDiagnostics";
import { MediaItem } from "@/types/media";
import { Link } from "react-router-dom";
import PromoBannerContainer from "@/components/PromoBannerContainer";
import { scrollToTop } from "@/utils/scrollToTop";
import { getHomepageContent } from "@/utils/contentFilters";
import { useDynamicHomepage, getFallbackHomepageContent } from "@/utils/dynamicHomepage";
// Adblocker awareness tracking imports removed
import { Button } from "@/components/ui/button";
import { ArrowUp } from "lucide-react";
import apiService from "@/services/apiService";

const Index = () => {
  // Function to determine the correct URL for "Show All" links
  const getShowAllUrl = (sectionSlug: string): string => {
    switch (sectionSlug) {
      case 'movies':
        return '/movies'; // AllMovies page
      case 'series':
      case 'web-series':
        return '/series'; // AllSeries page (shows all web series regardless of language)
      case 'requested':
        return '/category/requested'; // CategoryPage for Requested (AllRequested page doesn't exist in prod)
      case 'new-releases':
        return '/category/new-releases'; // CategoryPage for New Releases
      case 'drama':
        return '/category/drama'; // CategoryPage for Drama
      case 'hindi-movies':
        return '/category/hindi-movies'; // CategoryPage for Hindi Movies
      case 'hindi-web-series':
        return '/category/hindi-web-series'; // CategoryPage for Hindi Web Series
      case 'english-movies':
        return '/category/english-movies'; // CategoryPage for English Movies
      case 'english-web-series':
        return '/category/english-web-series'; // CategoryPage for English Web Series
      case 'telugu-movies':
        return '/category/telugu-movies'; // CategoryPage for Telugu Movies
      case 'telugu-web-series':
        return '/category/telugu-web-series'; // CategoryPage for Telugu Web Series
      case 'tamil-movies':
        return '/category/tamil-movies'; // CategoryPage for Tamil Movies
      case 'tamil-web-series':
        return '/category/tamil-web-series'; // CategoryPage for Tamil Web Series
      case 'malayalam-movies':
        return '/category/malayalam-movies'; // CategoryPage for Malayalam Movies
      case 'malayalam-web-series':
        return '/category/malayalam-web-series'; // CategoryPage for Malayalam Web Series
      case 'korean-movies':
        return '/category/korean-movies'; // CategoryPage for Korean Movies
      case 'korean-web-series':
        return '/category/korean-web-series'; // CategoryPage for Korean Web Series
      case 'japanese-movies':
        return '/category/japanese-movies'; // CategoryPage for Japanese Movies
      case 'japanese-web-series':
        return '/category/japanese-web-series'; // CategoryPage for Japanese Web Series
      case 'anime':
        return '/category/anime'; // CategoryPage for Anime
      case 'hindi-dubbed':
        return '/category/hindi-dubbed'; // CategoryPage for Hindi Dubbed
      case 'english-dubbed':
        return '/category/english-dubbed'; // CategoryPage for English Dubbed
      case 'animation':
        return '/category/animation'; // CategoryPage for Animation
      default:
        // For any other section, assume it's a category
        return `/category/${sectionSlug}`;
    }
  };

  // State for content data (fallback)
  const [contentData, setContentData] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fallbackContent, setFallbackContent] = useState<any>(null);
  const [showBackToTop, setShowBackToTop] = useState(false);

  // Use dynamic homepage content
  const dynamicHomepage = useDynamicHomepage();

  // Load fallback content when contentData changes
  useEffect(() => {
    const loadFallbackContent = async () => {
      if (contentData.length > 0) {
        const content = await getFallbackHomepageContent(contentData);
        setFallbackContent(content);
      }
    };
    loadFallbackContent();
  }, [contentData]);

  // Use dynamic content if available, otherwise fallback
  const homepageData = dynamicHomepage.error ? (fallbackContent || { carousel: [], sections: [] }) : dynamicHomepage;

  // Adblocker awareness popup state removed

  // Test modal state removed

  // Fetch content data from API
  useEffect(() => {
    const fetchContent = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check if we need to force refresh due to content updates
        const forceRefresh = localStorage.getItem('forceHomepageRefresh') === 'true';
        if (forceRefresh) {
          // Clear the flag and any cached data
          localStorage.removeItem('forceHomepageRefresh');
          // Clear any cached homepage data
          const cacheKeys = Object.keys(localStorage).filter(key => 
            key.includes('homepage') || key.includes('section') || key.includes('content')
          );
          cacheKeys.forEach(key => localStorage.removeItem(key));
        }

        const response = await apiService.getContent({
          published: true,
          limit: 100, // Get enough content for all sections
          sort_by: 'created_at',
          sort_order: 'desc'
        });

        if (response && response.success && Array.isArray(response.data)) {
          setContentData(response.data);
        } else {
          console.warn('No content data received from API, using empty array');
          setContentData([]);
        }
      } catch (error) {
        console.error('Error fetching content:', error);
        setError('Failed to load content');
        setContentData([]); // Fallback to empty array
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
    
    // Listen for storage events to refresh when content is updated from admin panel
    const handleStorageChange = (e) => {
      if (e.key === 'forceHomepageRefresh' && e.newValue === 'true') {
        fetchContent();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Back to Top functionality
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Adblocker awareness popup useEffect and handler removed

  // Show loading state
  if (loading) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <div className="h-7 md:h-10" />
        <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center text-muted-foreground">
              <div className="text-4xl mb-4">🎬</div>
              <p className="text-lg">Loading content...</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <div className="h-7 md:h-10" />
        <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center text-muted-foreground">
              <div className="text-4xl mb-4">⚠️</div>
              <p className="text-lg mb-2">Failed to load content</p>
              <p className="text-sm">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4"
              >
                Retry
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />

      {/* Added spacing here after Header */}
      <div className="h-7 md:h-10" />

      <main className="flex-1 w-full max-w-7xl mx-auto px-2 sm:px-3 py-1.5 sm:py-3"> {/* Reduced padding for more compact layout */}
        {/* Reduced margin-bottom for HeroCarousel */}
        <div className="mb-4 sm:mb-6 md:mb-8"> {/* Reduced from mb-6 sm:mb-9 md:mb-14 */}
          <HeroCarousel contentData={homepageData.carouselContent} />
        </div>

        {/* Reduced gap between sections */}
        <div className="mb-6 sm:mb-8"> {/* Reduced from mb-8 sm:mb-12 */}
          <PromoBannerContainer />
        </div>

        {/* Dynamic Content Sections */}
        {homepageData.isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3 text-muted-foreground">Loading content sections...</span>
          </div>
        ) : homepageData.error ? (
          <div className="text-center py-12">
            <p className="text-destructive mb-4">Failed to load dynamic content: {homepageData.error}</p>
            <p className="text-muted-foreground">Showing fallback content...</p>
          </div>
        ) : null}

        {homepageData.sections.map((section, index) => (
          <section key={section.id} className="stdb-section mb-6 sm:mb-8"> {/* Reduced from mb-10 sm:mb-14 */}
            <div className="flex items-end justify-between mb-2 sm:mb-3"> {/* Reduced from mb-3 sm:mb-4 */}
              <h3
                className="
                  text-xl sm:text-2xl md:text-3xl
                  font-black
                  font-mono
                  text-primary
                  drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)]
                  tracking-wide
                  uppercase
                  leading-tight
                  select-none
                  " /* Reduced font sizes from text-2xl sm:text-3xl md:text-4xl */
                style={{
                  letterSpacing: "1px",
                  color: section.color
                }}
              >
                {section.name}
              </h3>
              {section.showViewAll && (
                <Link
                  to={getShowAllUrl(section.slug)}
                  className="text-primary font-bold underline underline-offset-4 text-sm sm:text-base hover:opacity-80 transition flex-shrink-0"
                  onClick={scrollToTop}
                  style={{ color: section.color }}
                >
                  Show all ({section.totalCount})
                </Link>
              )}
            </div>
            <CardGrid items={section.content} />
          </section>
        ))}

        {/* Show message if no sections available */}
        {!homepageData.isLoading && !homepageData.error && homepageData.sections.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No content sections configured. Please contact the administrator.</p>
          </div>
        )}
      </main>

      <Footer />

      {/* Back to Top Button */}
      {showBackToTop && (
        <Button
          onClick={scrollToTop}
          className="fixed bottom-4 right-4 sm:bottom-6 sm:right-6 z-40 h-12 w-12 rounded-full bg-primary hover:bg-primary/90 shadow-lg transition-all duration-300"
          size="icon"
          aria-label="Back to top"
        >
          <ArrowUp className="h-5 w-5" />
        </Button>
      )}

      {/* Development test button removed */}

      {/* Adblocker awareness popup and test modal removed */}
    </div>
  );
};

export default Index;
